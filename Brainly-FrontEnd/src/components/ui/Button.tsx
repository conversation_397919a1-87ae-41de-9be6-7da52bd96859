import type { ReactElement } from "react";

interface ButtonProps {
    variant: "primary" | "secondary"
    text: string;
    startIcon: ReactElement;

}

const variantClasses = {
    "primary": "bg-purple-500 text-white", 
    "secondary": "bg-gray-100 text-purple-500"
}

export function Button({variant, text }: ButtonProps): ReactElement {
    return(
        <button className={variantClasses[variant]}>
            {text}
        </button>
    )
}