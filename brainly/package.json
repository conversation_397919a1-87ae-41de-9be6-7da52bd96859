{"name": "brainly", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"build": "tsc -b", "start": "node dist/index.js", "dev": "npm run build && npm run start"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.1", "typescript": "^5.9.2"}}